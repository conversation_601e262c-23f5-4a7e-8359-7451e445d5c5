<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>FeastMeet - 酷酷年轻人的约饭神器</title>
  <style>
    :root {
      /* 主色调 */
      --primary: #8C52FF;
      --primary-dark: #6E3AD9;
      --primary-light: #A875FF;
      
      /* 霓虹色调 */
      --neon-pink: #FF2E93;
      --neon-blue: #00E9FF;
      --neon-green: #00FF85;
      --neon-yellow: #FFDE59;
      
      /* 暗色背景 */
      --bg-dark: #121212;
      --bg-card: #1E1E1E;
      --bg-card-hover: #252525;
      
      /* 文本颜色 */
      --text-primary: #FFFFFF;
      --text-secondary: rgba(255, 255, 255, 0.7);
      --text-tertiary: rgba(255, 255, 255, 0.5);
      --text-disabled: rgba(255, 255, 255, 0.3);
      
      /* 边框与阴影 */
      --border-light: rgba(255, 255, 255, 0.1);
      --shadow-card: 0 4px 20px rgba(0, 0, 0, 0.25);
      --shadow-neon: 0 0 15px rgba(140, 82, 255, 0.5);
      
      /* 间距 */
      --space-xs: 4px;
      --space-sm: 8px;
      --space-md: 16px;
      --space-lg: 24px;
      --space-xl: 32px;
      
      /* 圆角 */
      --radius-sm: 8px;
      --radius-md: 12px;
      --radius-lg: 20px;
      --radius-full: 9999px;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Outfit', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
    }
    
    body {
      background-color: var(--bg-dark);
      color: var(--text-primary);
      line-height: 1.5;
      -webkit-font-smoothing: antialiased;
      padding: var(--space-md);
    }
    
    .app-container {
      display: flex;
      flex-wrap: wrap;
      gap: var(--space-xl);
      justify-content: center;
      padding-bottom: var(--space-xl);
    }
    
    .screen {
      width: 360px;
      height: 720px;
      background: var(--bg-dark);
      border-radius: var(--radius-lg);
      overflow: hidden;
      position: relative;
      box-shadow: var(--shadow-card);
      border: 1px solid var(--border-light);
      display: none;
    }
    
    .screen.active {
      display: block;
    }

    #current-screen {
      transition: all 0.15s ease;
    }
    
    .header {
      padding: var(--space-md) var(--space-md);
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      z-index: 10;
    }
    
    .header-title {
      font-size: 20px;
      font-weight: 600;
    }
    
    .header-action {
      display: flex;
      gap: var(--space-sm);
    }
    
    .icon-button {
      width: 40px;
      height: 40px;
      border-radius: var(--radius-full);
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.1);
      color: var(--text-primary);
      border: none;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .icon-button:hover, .icon-button:active {
      background: rgba(255, 255, 255, 0.2);
    }
    
    .content {
      height: calc(100% - 160px);
      overflow-y: auto;
      padding: 0 var(--space-md) var(--space-md);
    }
    
    .full-content {
      height: calc(100% - 80px);
      overflow-y: auto;
      padding: 0 var(--space-md) var(--space-md);
    }
    
    .nav-bar {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 80px;
      background: var(--bg-card);
      display: flex;
      justify-content: space-around;
      align-items: center;
      border-top: 1px solid var(--border-light);
      padding: 0 var(--space-md);
    }
    
    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: var(--text-tertiary);
      text-decoration: none;
      font-size: 12px;
      cursor: pointer;
    }
    
    .nav-item.active {
      color: var(--primary);
    }
    
    .nav-icon {
      font-size: 24px;
      margin-bottom: var(--space-xs);
    }
    
    .card {
      background: var(--bg-card);
      border-radius: var(--radius-md);
      padding: var(--space-md);
      margin-bottom: var(--space-md);
      border: 1px solid var(--border-light);
      transition: all 0.2s ease;
      cursor: pointer;
    }
    
    .card:active {
      transform: scale(0.98);
      background: var(--bg-card-hover);
    }
    
    .feast-card {
      background: var(--bg-card);
      border-radius: var(--radius-md);
      margin-bottom: var(--space-md);
      overflow: hidden;
      position: relative;
      box-shadow: var(--shadow-card);
      cursor: pointer;
      text-decoration: none;
      color: inherit;
    }
    
    .feast-image {
      height: 150px;
      position: relative;
      overflow: hidden;
    }
    
    .feast-image-bg {
      width: 100%;
      height: 100%;
      background-position: center;
      background-size: cover;
      filter: brightness(0.7);
    }
    
    .feast-details {
      padding: var(--space-md);
    }
    
    .feast-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--space-sm);
    }
    
    .feast-date {
      font-size: 14px;
      color: var(--text-secondary);
      display: flex;
      align-items: center;
    }
    
    .feast-icon {
      margin-right: var(--space-xs);
    }
    
    .tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: var(--space-xs);
      margin: var(--space-sm) 0;
    }
    
    .tag {
      background: rgba(255, 255, 255, 0.1);
      color: var(--text-secondary);
      padding: var(--space-xs) var(--space-sm);
      border-radius: var(--radius-full);
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .tag.highlighted {
      background: var(--primary);
      color: white;
    }
    
    .button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: var(--space-md) var(--space-lg);
      border-radius: var(--radius-full);
      font-weight: 600;
      border: none;
      cursor: pointer;
      transition: all 0.2s ease;
      text-decoration: none;
    }
    
    .button.primary {
      background: var(--primary);
      color: white;
    }
    
    .button.primary:hover, .button.primary:active {
      background: var(--primary-dark);
    }
    
    .button.outline {
      background: transparent;
      border: 1px solid var(--primary);
      color: var(--primary);
    }
    
    .button.outline:hover, .button.outline:active {
      background: rgba(140, 82, 255, 0.1);
    }
    
    .button.full {
      width: 100%;
    }
    
    .button-icon {
      margin-right: var(--space-sm);
    }
    
    .gradient-text {
      background: linear-gradient(to right, var(--neon-pink), var(--primary));
      background-clip: text;
      -webkit-background-clip: text;
      color: transparent;
      font-weight: 700;
    }
    
    .section-title {
      font-size: 18px;
      font-weight: 600;
      margin: var(--space-lg) 0 var(--space-md);
      display: flex;
      align-items: center;
    }
    
    .avatar {
      width: 50px;
      height: 50px;
      border-radius: var(--radius-full);
      object-fit: cover;
    }
    
    .avatar.small {
      width: 36px;
      height: 36px;
    }
    
    .login-logo {
      font-size: 36px;
      font-weight: 800;
      text-align: center;
      margin: var(--space-xl) 0;
    }

    .social-login {
      display: flex;
      justify-content: center;
      gap: var(--space-md);
      margin: var(--space-xl) 0;
    }

    .social-icon {
      width: 50px;
      height: 50px;
      background: var(--bg-card);
      border-radius: var(--radius-full);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: var(--text-primary);
      border: none;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .social-icon:hover {
      background: var(--bg-card-hover);
    }

    .divider {
      display: flex;
      align-items: center;
      color: var(--text-tertiary);
      margin: var(--space-xl) 0;
    }

    .divider:before, .divider:after {
      content: "";
      flex: 1;
      height: 1px;
      background: var(--border-light);
    }

    .divider:before {
      margin-right: var(--space-md);
    }

    .divider:after {
      margin-left: var(--space-md);
    }

    .form-group {
      margin-bottom: var(--space-lg);
    }

    .form-input {
      width: 100%;
      padding: var(--space-md);
      background: var(--bg-card);
      border: 1px solid var(--border-light);
      border-radius: var(--radius-md);
      color: var(--text-primary);
      font-size: 16px;
    }

    .form-input:focus {
      outline: none;
      border-color: var(--primary);
    }

    .form-label {
      display: block;
      margin-bottom: var(--space-sm);
      color: var(--text-secondary);
      font-weight: 500;
    }

    .search-bar {
      display: flex;
      align-items: center;
      background: var(--bg-card);
      border-radius: var(--radius-full);
      padding: var(--space-sm) var(--space-md);
      margin-bottom: var(--space-md);
    }

    .search-icon {
      color: var(--text-tertiary);
      margin-right: var(--space-sm);
    }

    .search-input {
      background: transparent;
      border: none;
      color: var(--text-primary);
      flex: 1;
      font-size: 16px;
    }

    .search-input:focus {
      outline: none;
    }

    .categories {
      display: flex;
      overflow-x: auto;
      gap: var(--space-sm);
      padding: var(--space-xs) 0;
      margin-bottom: var(--space-md);
      scrollbar-width: none;
    }

    .categories::-webkit-scrollbar {
      display: none;
    }

    .category-item {
      padding: var(--space-sm) var(--space-md);
      background: var(--bg-card);
      border-radius: var(--radius-full);
      white-space: nowrap;
      color: var(--text-secondary);
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .category-item.active {
      background: var(--primary);
      color: white;
    }

    .create-feast-fab {
      position: absolute;
      bottom: 100px;
      right: var(--space-md);
      width: 60px;
      height: 60px;
      border-radius: var(--radius-full);
      background: var(--primary);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: var(--shadow-neon);
      border: none;
      font-size: 24px;
      z-index: 100;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .create-feast-fab:hover {
      background: var(--primary-dark);
      transform: scale(1.05);
    }

    .profile-header {
      background: linear-gradient(to right, var(--neon-blue), var(--primary));
      padding: var(--space-xl) var(--space-md) var(--space-lg);
      margin: -16px -16px 0;
      text-align: center;
      position: relative;
    }

    .profile-avatar {
      width: 100px;
      height: 100px;
      border-radius: var(--radius-full);
      border: 3px solid white;
      margin-bottom: var(--space-sm);
    }

    .profile-stats {
      display: flex;
      justify-content: space-around;
      margin: var(--space-md) 0;
    }

    .stat-item {
      text-align: center;
    }

    .stat-value {
      font-size: 24px;
      font-weight: 700;
    }

    .stat-label {
      font-size: 12px;
      color: var(--text-tertiary);
    }

    /* Animations */
    @keyframes slideIn {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }

    @keyframes pulse {
      0% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(0, 255, 133, 0.5);
      }
      70% {
        transform: scale(1);
        box-shadow: 0 0 0 10px rgba(0, 255, 133, 0);
      }
      100% {
        transform: scale(0.95);
        box-shadow: 0 0 0 0 rgba(0, 255, 133, 0);
      }
    }

    .pulse {
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: var(--radius-full);
      background: var(--neon-green);
      margin-right: var(--space-sm);
      animation: pulse 1.5s infinite;
    }

    /* Loading states */
    .button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    /* Hover effects */
    .feast-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }

    .card:hover {
      background: var(--bg-card-hover);
    }

    /* Mobile optimizations */
    @media (max-width: 480px) {
      body {
        padding: 0;
      }

      .app-container {
        gap: 0;
        padding: 0;
      }

      .screen {
        width: 100vw;
        height: 100vh;
        border-radius: 0;
        border: none;
      }
    }

    /* Touch improvements */
    @media (hover: none) and (pointer: coarse) {
      .card:hover {
        background: var(--bg-card);
      }

      .feast-card:hover {
        transform: none;
        box-shadow: var(--shadow-card);
      }

      .icon-button:hover {
        background: rgba(255, 255, 255, 0.1);
      }
    }

    /* Accessibility improvements */
    .button:focus,
    .icon-button:focus,
    .nav-item:focus {
      outline: 2px solid var(--primary);
      outline-offset: 2px;
    }

    .form-input:focus {
      box-shadow: 0 0 0 2px rgba(140, 82, 255, 0.3);
    }

    /* Material Icons */
    .material-icons {
      font-family: 'Material Icons';
      font-weight: normal;
      font-style: normal;
      font-size: 24px;
      display: inline-block;
      line-height: 1;
      text-transform: none;
      letter-spacing: normal;
      word-wrap: normal;
      white-space: nowrap;
      direction: ltr;
      -webkit-font-smoothing: antialiased;
      text-rendering: optimizeLegibility;
    }
  </style>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>

<div class="app-container">
  <!-- Single screen container that will show different screens -->
  <div class="screen active" id="current-screen">
    <!-- Content will be dynamically loaded here -->
  </div>
</div>

<script>
// Screen management and navigation system
const screens = {};
let currentScreen = 'login';

// Initialize the app
document.addEventListener('DOMContentLoaded', function() {
  initializeScreens();
  showScreen('login');
});

function initializeScreens() {
  // Login Screen
  screens.login = `
    <div class="full-content">
      <div style="height: 30%"></div>

      <div class="login-logo">
        <span class="gradient-text">FeastMeet</span>
        <div style="font-size: 16px; color: var(--text-secondary); font-weight: normal; margin-top: 8px;">美食相遇，灵魂碰撞</div>
      </div>

      <div class="social-login">
        <button class="social-icon" onclick="showScreen('home')">
          <span class="material-icons">alternate_email</span>
        </button>
        <button class="social-icon" onclick="showScreen('home')">
          <span class="material-icons">chat</span>
        </button>
        <button class="social-icon" onclick="showScreen('home')">
          <span class="material-icons">language</span>
        </button>
      </div>

      <div class="divider">或使用邮箱登录</div>

      <div class="form-group">
        <input type="email" class="form-input" placeholder="邮箱地址">
      </div>

      <div class="form-group">
        <input type="password" class="form-input" placeholder="密码">
      </div>

      <button class="button primary full" style="margin-bottom: var(--space-md);" onclick="showScreen('home')">登录</button>
      <button class="button outline full" onclick="showScreen('home')">注册新账号</button>

      <div style="text-align: center; margin-top: var(--space-xl); color: var(--text-tertiary);">
        登录即表示您同意我们的<br>
        <a href="#" style="color: var(--primary);">服务条款</a> 和 <a href="#" style="color: var(--primary);">隐私政策</a>
      </div>
    </div>
  `;

  // Home Screen
  screens.home = `
    <div class="header">
      <div class="header-title">探索饭局</div>
      <div class="header-action">
        <button class="icon-button">
          <span class="material-icons">notifications</span>
        </button>
        <button class="icon-button">
          <span class="material-icons">tune</span>
        </button>
      </div>
    </div>

    <div class="content">
      <div class="search-bar">
        <span class="material-icons search-icon">search</span>
        <input type="text" class="search-input" placeholder="搜索饭局、餐厅或美食...">
      </div>

      <div class="categories">
        <div class="category-item active">推荐</div>
        <div class="category-item">新奇体验</div>
        <div class="category-item">职场社交</div>
        <div class="category-item">音乐爱好者</div>
        <div class="category-item">电影交流</div>
        <div class="category-item">艺术文化</div>
      </div>

      <div class="feast-card" onclick="showScreen('feast-detail')">
        <div class="feast-image">
          <div class="feast-image-bg" style="background-image: url('https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80');"></div>
          <div style="position: absolute; top: var(--space-md); right: var(--space-md); background: rgba(0,0,0,0.6); padding: var(--space-xs) var(--space-sm); border-radius: var(--radius-full); font-size: 12px; color: white;">
            <span class="material-icons" style="font-size: 14px; vertical-align: middle; margin-right: 2px;">person</span> 3/6
          </div>
        </div>
        <div class="feast-details">
          <h3>创意料理夜 @ 深蓝餐厅</h3>
          <div class="feast-meta">
            <div class="feast-date">
              <span class="material-icons feast-icon">event</span>
              今晚 19:30
            </div>
            <div class="feast-date">
              <span class="material-icons feast-icon">location_on</span>
              1.2km
            </div>
          </div>
          <p style="color: var(--text-secondary); margin-bottom: var(--space-sm);">分享对创意料理的热爱，探讨未来美食趋势，交流味蕾体验。</p>
          <div class="tags-container">
            <span class="tag highlighted">美食探索</span>
            <span class="tag">创意料理</span>
            <span class="tag">社交晚餐</span>
          </div>
          <div style="display: flex; align-items: center; margin-top: var(--space-md); justify-content: space-between;">
            <div style="display: flex; align-items: center;">
              <img src="https://randomuser.me/api/portraits/men/32.jpg" class="avatar small">
              <div style="margin-left: var(--space-sm); font-size: 14px;">
                <div>Alex Chen</div>
                <div style="color: var(--text-tertiary); font-size: 12px; display: flex; align-items: center;">
                  <span class="material-icons" style="font-size: 14px; margin-right: 2px;">star</span>
                  4.8
                </div>
              </div>
            </div>
            <div style="color: var(--neon-pink); font-weight: 600;">¥128/位</div>
          </div>
        </div>
      </div>

      <button class="create-feast-fab" onclick="showScreen('create-feast')">
        <span class="material-icons">add</span>
      </button>
    </div>

    <div class="nav-bar">
      <div class="nav-item active" onclick="showScreen('home')">
        <span class="material-icons nav-icon">explore</span>
        <span>发现</span>
      </div>
      <div class="nav-item" onclick="showScreen('nearby')">
        <span class="material-icons nav-icon">map</span>
        <span>附近</span>
      </div>
      <div class="nav-item" onclick="showScreen('messages')">
        <span class="material-icons nav-icon">chat</span>
        <span>消息</span>
      </div>
      <div class="nav-item" onclick="showScreen('profile')">
        <span class="material-icons nav-icon">person</span>
        <span>我的</span>
      </div>
    </div>
  `;

  // Feast Detail Screen
  screens['feast-detail'] = `
    <div class="header">
      <button class="icon-button" onclick="showScreen('home')">
        <span class="material-icons">arrow_back</span>
      </button>
      <div class="header-title">饭局详情</div>
      <div class="header-action">
        <button class="icon-button">
          <span class="material-icons">share</span>
        </button>
      </div>
    </div>

    <div class="full-content">
      <div style="height: 200px; position: relative; margin: -16px -16px 16px;">
        <div style="position: absolute; inset: 0; background-image: url('https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80'); background-size: cover; background-position: center;"></div>
        <div style="position: absolute; inset: 0; background: linear-gradient(to top, rgba(18,18,18,1) 0%, rgba(18,18,18,0) 100%);"></div>
      </div>

      <h2 style="margin-bottom: var(--space-sm);">创意料理夜 @ 深蓝餐厅</h2>

      <div class="feast-meta" style="margin-bottom: var(--space-md);">
        <div class="feast-date">
          <span class="material-icons feast-icon">event</span>
          今晚 19:30-21:30
        </div>
        <div class="feast-date">
          <span class="material-icons feast-icon">location_on</span>
          深蓝餐厅 · 1.2km
        </div>
      </div>

      <div class="card">
        <h4 style="margin-bottom: var(--space-sm);">关于这场饭局</h4>
        <p style="color: var(--text-secondary); line-height: 1.6;">分享对创意料理的热爱，探讨未来美食趋势，交流味蕾体验。今晚的主厨特别推出分子料理新菜单，我们将共同品尝并进行有趣的美食讨论。</p>
        <p style="color: var(--text-secondary); line-height: 1.6; margin-top: var(--space-sm);">适合喜欢尝试新事物、对料理有热情的朋友。席间将有轻松的交流环节，不用担心尴尬。</p>

        <div style="margin-top: var(--space-md);">
          <div class="tags-container">
            <span class="tag highlighted">美食探索</span>
            <span class="tag">创意料理</span>
            <span class="tag">社交晚餐</span>
            <span class="tag">分子料理</span>
          </div>
        </div>
      </div>

      <div class="card">
        <h4 style="margin-bottom: var(--space-md);">主办人</h4>
        <div style="display: flex; align-items: center;">
          <img src="https://randomuser.me/api/portraits/men/32.jpg" class="avatar">
          <div style="margin-left: var(--space-md);">
            <div style="font-weight: 500;">Alex Chen</div>
            <div style="color: var(--text-tertiary); font-size: 14px; margin-top: var(--space-xs);">美食博主 | 料理爱好者</div>
            <div style="display: flex; align-items: center; margin-top: var(--space-xs);">
              <span class="material-icons" style="font-size: 16px; color: var(--neon-yellow); margin-right: 2px;">star</span>
              <span>4.8</span>
              <span style="margin-left: var(--space-xs); color: var(--text-tertiary);">(已举办32场)</span>
            </div>
          </div>
        </div>
      </div>

      <div style="margin-bottom: var(--space-xl); padding: var(--space-md) 0;">
        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-md);">
          <div>
            <div style="color: var(--text-tertiary);">人均费用</div>
            <div style="font-size: 20px; font-weight: 600; color: var(--neon-pink);">¥128</div>
          </div>
          <div>
            <div style="color: var(--text-tertiary);">剩余名额</div>
            <div style="font-size: 20px; font-weight: 600;">3位</div>
          </div>
        </div>

        <button class="button primary full" onclick="alert('加入成功！')">
          <span class="button-icon material-icons">check_circle</span>
          立即加入
        </button>
      </div>
    </div>
  `;

  // Create Feast Screen
  screens['create-feast'] = `
    <div class="header">
      <button class="icon-button" onclick="showScreen('home')">
        <span class="material-icons">arrow_back</span>
      </button>
      <div class="header-title">创建新饭局</div>
    </div>

    <div class="full-content">
      <div class="form-group">
        <label class="form-label">饭局标题</label>
        <input type="text" class="form-input" placeholder="给你的饭局起个吸引人的名字">
      </div>

      <div class="form-group">
        <label class="form-label">饭局时间</label>
        <div style="display: flex; gap: var(--space-md);">
          <input type="date" class="form-input" style="flex: 1;">
          <input type="time" class="form-input" style="flex: 1;">
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">选择餐厅</label>
        <div style="position: relative;">
          <input type="text" class="form-input" placeholder="搜索餐厅...">
          <span class="material-icons" style="position: absolute; right: var(--space-md); top: 50%; transform: translateY(-50%); color: var(--text-tertiary);">search</span>
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">饭局描述</label>
        <textarea class="form-input" placeholder="描述一下你的饭局内容、氛围、适合什么样的人参加等..." style="min-height: 100px; resize: vertical;"></textarea>
      </div>

      <div class="form-group">
        <label class="form-label">添加标签 (最多选择3个)</label>
        <div class="tags-container" style="margin-top: 0;">
          <span class="tag highlighted" onclick="toggleTag(this)">美食探索</span>
          <span class="tag" onclick="toggleTag(this)">创意料理</span>
          <span class="tag" onclick="toggleTag(this)">聊天交友</span>
          <span class="tag" onclick="toggleTag(this)">职场社交</span>
          <span class="tag" onclick="toggleTag(this)">音乐</span>
          <span class="tag" onclick="toggleTag(this)">电影</span>
          <span class="tag" onclick="toggleTag(this)">艺术</span>
          <span class="tag" onclick="toggleTag(this)">读书会</span>
        </div>
      </div>

      <button class="button primary full" style="margin: var(--space-xl) 0;" onclick="createFeast()">
        <span class="button-icon material-icons">celebration</span>
        发布饭局
      </button>
    </div>
  `;

  // Nearby Screen
  screens.nearby = `
    <div class="header">
      <div class="header-title">附近饭局</div>
      <div class="header-action">
        <button class="icon-button">
          <span class="material-icons">filter_list</span>
        </button>
      </div>
    </div>

    <div style="position: absolute; top: 80px; left: 0; right: 0; bottom: 80px; background: var(--bg-card); display: flex; align-items: center; justify-content: center;">
      <div style="text-align: center; color: var(--text-secondary);">
        <span class="material-icons" style="font-size: 48px; margin-bottom: var(--space-md);">map</span>
        <div>地图功能开发中...</div>
      </div>
    </div>

    <div class="nav-bar">
      <div class="nav-item" onclick="showScreen('home')">
        <span class="material-icons nav-icon">explore</span>
        <span>发现</span>
      </div>
      <div class="nav-item active" onclick="showScreen('nearby')">
        <span class="material-icons nav-icon">map</span>
        <span>附近</span>
      </div>
      <div class="nav-item" onclick="showScreen('messages')">
        <span class="material-icons nav-icon">chat</span>
        <span>消息</span>
      </div>
      <div class="nav-item" onclick="showScreen('profile')">
        <span class="material-icons nav-icon">person</span>
        <span>我的</span>
      </div>
    </div>
  `;

  // Messages Screen
  screens.messages = `
    <div class="header">
      <div class="header-title">消息</div>
      <div class="header-action">
        <button class="icon-button">
          <span class="material-icons">edit</span>
        </button>
      </div>
    </div>

    <div class="content">
      <div class="search-bar">
        <span class="material-icons search-icon">search</span>
        <input type="text" class="search-input" placeholder="搜索消息...">
      </div>

      <div class="categories">
        <div class="category-item active">全部</div>
        <div class="category-item">饭局</div>
        <div class="category-item">好友</div>
        <div class="category-item">系统</div>
      </div>

      <div class="card" style="margin-bottom: var(--space-md); padding: var(--space-md);">
        <div style="display: flex; align-items: center;">
          <div style="position: relative;">
            <img src="https://randomuser.me/api/portraits/men/32.jpg" class="avatar">
            <span style="position: absolute; bottom: 0; right: 0; width: 12px; height: 12px; background: var(--neon-green); border-radius: 50%; border: 2px solid var(--bg-card);"></span>
          </div>
          <div style="margin-left: var(--space-md); flex: 1;">
            <div style="display: flex; justify-content: space-between;">
              <div style="font-weight: 500;">Alex Chen</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">14:30</div>
            </div>
            <div style="display: flex; justify-content: space-between; margin-top: var(--space-xs);">
              <div style="color: var(--text-secondary); font-size: 14px;">期待今晚见到你！我已经准备...</div>
              <div style="background: var(--primary); color: white; font-size: 12px; height: 20px; width: 20px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">2</div>
            </div>
          </div>
        </div>
      </div>

      <div class="card" style="margin-bottom: var(--space-md); padding: var(--space-md);">
        <div style="display: flex; align-items: center;">
          <div style="position: relative;">
            <div style="width: 50px; height: 50px; border-radius: var(--radius-md); background-image: url('https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80'); background-size: cover; background-position: center;"></div>
          </div>
          <div style="margin-left: var(--space-md); flex: 1;">
            <div style="display: flex; justify-content: space-between;">
              <div style="font-weight: 500;">创意料理夜 (6人)</div>
              <div style="font-size: 12px; color: var(--text-tertiary);">12:05</div>
            </div>
            <div style="display: flex; justify-content: space-between; margin-top: var(--space-xs);">
              <div style="color: var(--text-secondary); font-size: 14px;">Alex: 大家可以提前10分钟到餐厅...</div>
              <div style="background: var(--primary); color: white; font-size: 12px; height: 20px; width: 20px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">5</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="nav-bar">
      <div class="nav-item" onclick="showScreen('home')">
        <span class="material-icons nav-icon">explore</span>
        <span>发现</span>
      </div>
      <div class="nav-item" onclick="showScreen('nearby')">
        <span class="material-icons nav-icon">map</span>
        <span>附近</span>
      </div>
      <div class="nav-item active" onclick="showScreen('messages')">
        <span class="material-icons nav-icon">chat</span>
        <span>消息</span>
      </div>
      <div class="nav-item" onclick="showScreen('profile')">
        <span class="material-icons nav-icon">person</span>
        <span>我的</span>
      </div>
    </div>
  `;

  // Profile Screen
  screens.profile = `
    <div class="profile-header">
      <img src="https://randomuser.me/api/portraits/women/33.jpg" class="profile-avatar">
      <h2>Lisa Wang</h2>
      <div style="color: rgba(255,255,255,0.7);">美食探险家 | 摄影爱好者</div>
    </div>

    <div class="content" style="padding-top: 0;">
      <div class="profile-stats">
        <div class="stat-item">
          <div class="stat-value">15</div>
          <div class="stat-label">已参与饭局</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">8</div>
          <div class="stat-label">已举办饭局</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">4.9</div>
          <div class="stat-label">平均评分</div>
        </div>
      </div>

      <div class="card">
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <div style="display: flex; align-items: center;">
            <span class="material-icons" style="margin-right: var(--space-md); color: var(--primary);">settings</span>
            <span>设置</span>
          </div>
          <span class="material-icons" style="color: var(--text-tertiary);">navigate_next</span>
        </div>
      </div>

      <div class="card">
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <div style="display: flex; align-items: center;">
            <span class="material-icons" style="margin-right: var(--space-md); color: var(--primary);">help</span>
            <span>帮助与反馈</span>
          </div>
          <span class="material-icons" style="color: var(--text-tertiary);">navigate_next</span>
        </div>
      </div>

      <button class="button outline full" style="margin-top: var(--space-xl);" onclick="showScreen('login')">
        退出登录
      </button>
    </div>

    <div class="nav-bar">
      <div class="nav-item" onclick="showScreen('home')">
        <span class="material-icons nav-icon">explore</span>
        <span>发现</span>
      </div>
      <div class="nav-item" onclick="showScreen('nearby')">
        <span class="material-icons nav-icon">map</span>
        <span>附近</span>
      </div>
      <div class="nav-item" onclick="showScreen('messages')">
        <span class="material-icons nav-icon">chat</span>
        <span>消息</span>
      </div>
      <div class="nav-item active" onclick="showScreen('profile')">
        <span class="material-icons nav-icon">person</span>
        <span>我的</span>
      </div>
    </div>
  `;
}

function showScreen(screenId) {
  currentScreen = screenId;
  const screenContainer = document.getElementById('current-screen');
  if (screens[screenId]) {
    screenContainer.innerHTML = screens[screenId];
    // Add event listeners after content is loaded
    addEventListeners();
  }
}

function addEventListeners() {
  // Update navigation active states
  updateNavigation();

  // Add category selection listeners
  const categoryItems = document.querySelectorAll('.category-item');
  categoryItems.forEach(item => {
    item.addEventListener('click', function() {
      categoryItems.forEach(cat => cat.classList.remove('active'));
      this.classList.add('active');
    });
  });

  // Add search functionality
  const searchInputs = document.querySelectorAll('.search-input');
  searchInputs.forEach(input => {
    input.addEventListener('input', function(e) {
      if (e.target.value.length > 2) {
        handleSearch(e.target.value);
      }
    });
  });

  // Add form validation
  const formInputs = document.querySelectorAll('.form-input');
  formInputs.forEach(input => {
    input.addEventListener('blur', function() {
      validateInput(this);
    });
  });
}

function validateInput(input) {
  const value = input.value.trim();

  if (input.type === 'email' && value) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      input.style.borderColor = 'var(--neon-pink)';
      showNotification('请输入有效的邮箱地址', 'error');
    } else {
      input.style.borderColor = 'var(--neon-green)';
    }
  }

  if (input.placeholder.includes('饭局标题') && value.length > 0 && value.length < 5) {
    input.style.borderColor = 'var(--neon-pink)';
    showNotification('饭局标题至少需要5个字符', 'error');
  }
}

function updateNavigation() {
  const navItems = document.querySelectorAll('.nav-item');
  navItems.forEach(item => {
    item.classList.remove('active');
  });

  // Add active class based on current screen
  const activeNavMap = {
    'home': 0,
    'nearby': 1,
    'messages': 2,
    'profile': 3
  };

  if (activeNavMap[currentScreen] !== undefined) {
    const activeIndex = activeNavMap[currentScreen];
    if (navItems[activeIndex]) {
      navItems[activeIndex].classList.add('active');
    }
  }
}

function toggleTag(tagElement) {
  const highlightedTags = document.querySelectorAll('.tag.highlighted');

  if (tagElement.classList.contains('highlighted')) {
    tagElement.classList.remove('highlighted');
  } else if (highlightedTags.length < 3) {
    tagElement.classList.add('highlighted');
  } else {
    alert('最多只能选择3个标签');
  }
}

function createFeast() {
  const title = document.querySelector('input[placeholder="给你的饭局起个吸引人的名字"]').value;
  if (!title.trim()) {
    alert('请输入饭局标题');
    return;
  }

  // Show loading state
  const button = document.querySelector('.button.primary.full');
  const originalText = button.innerHTML;
  button.innerHTML = '<span class="material-icons">hourglass_empty</span> 发布中...';
  button.disabled = true;

  // Simulate API call
  setTimeout(() => {
    alert('饭局创建成功！');
    showScreen('home');
  }, 1500);
}

// Add smooth transitions
function showScreen(screenId) {
  const screenContainer = document.getElementById('current-screen');

  // Add fade out effect
  screenContainer.style.opacity = '0.5';
  screenContainer.style.transform = 'scale(0.95)';

  setTimeout(() => {
    currentScreen = screenId;
    if (screens[screenId]) {
      screenContainer.innerHTML = screens[screenId];
      // Add event listeners after content is loaded
      addEventListeners();

      // Add fade in effect
      screenContainer.style.opacity = '1';
      screenContainer.style.transform = 'scale(1)';
    }
  }, 150);
}

// Add search functionality
function handleSearch(query) {
  if (query.trim()) {
    console.log('Searching for:', query);
    // Here you would implement actual search logic
  }
}

// Add notification system
function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.textContent = message;

  const bgColor = type === 'error' ? 'var(--neon-pink)' :
                  type === 'success' ? 'var(--neon-green)' : 'var(--primary)';

  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: ${bgColor};
    color: white;
    padding: var(--space-md);
    border-radius: var(--radius-md);
    z-index: 1000;
    animation: slideIn 0.3s ease;
    box-shadow: var(--shadow-card);
    font-weight: 500;
  `;

  document.body.appendChild(notification);

  setTimeout(() => {
    notification.style.animation = 'slideOut 0.3s ease';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 300);
  }, 3000);
}

// Add slide out animation
const slideOutKeyframes = `
  @keyframes slideOut {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
`;

// Inject the slideOut animation
const style = document.createElement('style');
style.textContent = slideOutKeyframes;
document.head.appendChild(style);
</script>

</body>
</html>
