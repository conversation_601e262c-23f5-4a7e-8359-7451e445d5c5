# FeastMeet - 酷酷年轻人的约饭神器

一个现代化的社交约饭应用，专为年轻人设计，让美食爱好者能够轻松发现和参与有趣的饭局活动。

## 🌟 特性

### 核心功能
- **探索饭局** - 浏览附近的精彩饭局活动
- **创建饭局** - 轻松发起自己的美食聚会
- **实时消息** - 与其他参与者即时沟通
- **个人资料** - 展示你的美食品味和社交经历

### 设计亮点
- 🎨 **霓虹暗黑主题** - 酷炫的视觉设计，符合年轻人审美
- 📱 **移动优先** - 完全响应式设计，完美适配手机屏幕
- ⚡ **流畅交互** - 平滑的页面切换和动画效果
- 🎯 **直观导航** - 简洁明了的用户界面

## 🚀 快速开始

### 在线预览
直接在浏览器中打开 `index.html` 文件即可体验完整功能。

### 本地运行
1. 克隆或下载项目文件
2. 在浏览器中打开 `index.html`
3. 开始探索 FeastMeet 的各项功能

## 📱 功能页面

### 1. 登录页面
- 社交媒体快速登录
- 邮箱密码登录
- 优雅的品牌展示

### 2. 主页/发现页
- 饭局卡片展示
- 分类筛选
- 搜索功能
- 浮动创建按钮

### 3. 饭局详情页
- 详细活动信息
- 主办人介绍
- 参与者列表
- 一键加入功能

### 4. 创建饭局页
- 表单验证
- 标签选择
- 实时预览
- 发布确认

### 5. 附近页面
- 地图视图（开发中）
- 位置筛选
- 距离显示

### 6. 消息页面
- 聊天列表
- 未读消息提醒
- 分类筛选

### 7. 个人资料页
- 统计数据
- 设置选项
- 退出登录

## 🎨 设计系统

### 色彩方案
- **主色调**: 紫色系 (#8C52FF)
- **霓虹色**: 粉色、蓝色、绿色、黄色
- **背景色**: 深色主题 (#121212, #1E1E1E)

### 组件库
- 卡片组件
- 按钮组件
- 表单组件
- 导航组件
- 标签组件

## 🔧 技术实现

### 前端技术
- **HTML5** - 语义化结构
- **CSS3** - 现代样式和动画
- **JavaScript** - 交互逻辑和状态管理

### 特色功能
- 单页应用架构
- 屏幕切换管理
- 表单验证
- 通知系统
- 响应式设计

## 📋 开发计划

### 已完成 ✅
- [x] 基础页面结构
- [x] 视觉设计实现
- [x] 页面导航系统
- [x] 交互功能
- [x] 响应式适配

### 待开发 🚧
- [ ] 后端API集成
- [ ] 用户认证系统
- [ ] 实时聊天功能
- [ ] 地图集成
- [ ] 推送通知
- [ ] 数据持久化

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个项目！

### 开发环境
- 任何现代浏览器
- 代码编辑器（推荐 VS Code）
- 可选：本地服务器（用于测试）

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- 设计灵感来源于现代移动应用趋势
- 图标使用 Google Material Icons
- 字体使用 Google Fonts (Outfit)
- 示例图片来源于 Unsplash

---

**FeastMeet** - 让每一次聚餐都成为美好回忆 🍽️✨
